#!/usr/bin/env python3
"""
PDF Reader Script
Reads and extracts text content from PDF files
"""

import PyPDF2
import sys
import os

def read_pdf(pdf_path):
    """
    Read PDF file and extract text content
    
    Args:
        pdf_path (str): Path to the PDF file
        
    Returns:
        str: Extracted text content
    """
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            print(f"PDF文件信息:")
            print(f"- 文件路径: {pdf_path}")
            print(f"- 总页数: {len(pdf_reader.pages)}")
            print(f"- 是否加密: {pdf_reader.is_encrypted}")
            print("-" * 50)
            
            text_content = ""
            
            for page_num, page in enumerate(pdf_reader.pages, 1):
                print(f"\n=== 第 {page_num} 页 ===")
                try:
                    page_text = page.extract_text()
                    if page_text.strip():
                        print(page_text)
                        text_content += f"\n=== 第 {page_num} 页 ===\n"
                        text_content += page_text + "\n"
                    else:
                        print("(此页无可提取的文本内容)")
                        text_content += f"\n=== 第 {page_num} 页 ===\n(此页无可提取的文本内容)\n"
                except Exception as e:
                    print(f"提取第 {page_num} 页时出错: {e}")
                    text_content += f"\n=== 第 {page_num} 页 ===\n提取错误: {e}\n"
                
                print("-" * 30)
            
            return text_content
            
    except FileNotFoundError:
        print(f"错误: 找不到文件 {pdf_path}")
        return None
    except Exception as e:
        print(f"读取PDF时出错: {e}")
        return None

def main():
    # PDF文件路径
    pdf_file = "Final_Assignment_vfinal (1).pdf"
    
    if not os.path.exists(pdf_file):
        print(f"错误: 文件 {pdf_file} 不存在")
        print("当前目录中的文件:")
        for file in os.listdir("."):
            if file.endswith(".pdf"):
                print(f"  - {file}")
        return
    
    print("开始读取PDF文件...")
    content = read_pdf(pdf_file)
    
    if content:
        # 保存提取的内容到文本文件
        output_file = "pdf_content_extracted.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"\n内容已保存到: {output_file}")

if __name__ == "__main__":
    main()
