#!/usr/bin/env python3
"""
Markdown to PDF Converter
Converts the assignment report from Markdown to PDF format
"""

import os
import sys
import subprocess
import platform

def check_pandoc():
    """Check if pandoc is available."""
    try:
        result = subprocess.run(['pandoc', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Pandoc is available")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ Pandoc not found")
    return False

def install_pandoc():
    """Provide instructions for installing pandoc."""
    system = platform.system().lower()
    
    print("\n📦 Pandoc Installation Instructions:")
    print("=" * 50)
    
    if system == "windows":
        print("Windows:")
        print("1. Download from: https://pandoc.org/installing.html")
        print("2. Or use chocolatey: choco install pandoc")
        print("3. Or use winget: winget install JohnMacFarlane.Pandoc")
    elif system == "linux":
        print("Linux:")
        print("Ubuntu/Debian: sudo apt-get install pandoc")
        print("CentOS/RHEL: sudo yum install pandoc")
        print("Fedora: sudo dnf install pandoc")
    else:
        print("macOS:")
        print("brew install pandoc")
    
    print("\nAfter installation, restart your terminal and run this script again.")

def convert_markdown_to_pdf(input_file, output_file):
    """Convert Markdown file to PDF using pandoc."""
    
    # Pandoc command with formatting options
    cmd = [
        'pandoc',
        input_file,
        '-o', output_file,
        '--pdf-engine=xelatex',  # Use XeLaTeX for better formatting
        '--variable', 'geometry:margin=1in',  # Set margins
        '--variable', 'fontsize=11pt',  # Set font size
        '--variable', 'linestretch=1.2',  # Set line spacing
        '--toc',  # Include table of contents
        '--toc-depth=3',  # TOC depth
        '--number-sections',  # Number sections
        '--highlight-style=tango',  # Code highlighting style
    ]
    
    try:
        print(f"🔄 Converting {input_file} to {output_file}...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ Successfully converted to {output_file}")
            return True
        else:
            print(f"❌ Conversion failed:")
            print(f"Error: {result.stderr}")
            
            # Try alternative method without XeLaTeX
            print("\n🔄 Trying alternative conversion method...")
            cmd_alt = [
                'pandoc',
                input_file,
                '-o', output_file,
                '--variable', 'geometry:margin=1in',
                '--variable', 'fontsize=11pt',
                '--toc',
                '--number-sections'
            ]
            
            result_alt = subprocess.run(cmd_alt, capture_output=True, text=True)
            if result_alt.returncode == 0:
                print(f"✅ Successfully converted using alternative method")
                return True
            else:
                print(f"❌ Alternative conversion also failed:")
                print(f"Error: {result_alt.stderr}")
                return False
                
    except Exception as e:
        print(f"❌ Error during conversion: {e}")
        return False

def create_html_version(input_file, output_file):
    """Create HTML version as backup."""
    cmd = [
        'pandoc',
        input_file,
        '-o', output_file,
        '--standalone',
        '--toc',
        '--number-sections',
        '--css', 'style.css'  # Optional CSS file
    ]
    
    try:
        print(f"🔄 Creating HTML version: {output_file}...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ HTML version created: {output_file}")
            return True
        else:
            print(f"❌ HTML creation failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error creating HTML: {e}")
        return False

def main():
    """Main conversion function."""
    input_file = "BIT1213_Final_Assignment_Report.md"
    pdf_output = "BIT1213_Final_Assignment_Report.pdf"
    html_output = "BIT1213_Final_Assignment_Report.html"
    
    print("📄 Assignment Report PDF Converter")
    print("=" * 40)
    
    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"❌ Input file not found: {input_file}")
        return False
    
    # Check pandoc availability
    if not check_pandoc():
        install_pandoc()
        return False
    
    # Convert to PDF
    pdf_success = convert_markdown_to_pdf(input_file, pdf_output)
    
    # Create HTML backup
    html_success = create_html_version(input_file, html_output)
    
    # Summary
    print("\n📋 Conversion Summary:")
    print("=" * 30)
    print(f"PDF conversion: {'✅ Success' if pdf_success else '❌ Failed'}")
    print(f"HTML backup: {'✅ Success' if html_success else '❌ Failed'}")
    
    if pdf_success:
        print(f"\n🎉 Assignment report ready for submission: {pdf_output}")
        
        # Check file size
        if os.path.exists(pdf_output):
            size_mb = os.path.getsize(pdf_output) / (1024 * 1024)
            print(f"📊 PDF file size: {size_mb:.2f} MB")
    
    return pdf_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
