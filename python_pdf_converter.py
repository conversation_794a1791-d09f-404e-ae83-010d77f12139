#!/usr/bin/env python3
"""
Python-based Markdown to PDF Converter
Alternative converter using Python libraries
"""

import os
import sys
import markdown
import pdfkit
from datetime import datetime

def create_html_from_markdown(md_file, html_file):
    """Convert Markdown to HTML using Python markdown library."""
    try:
        # Read markdown file
        with open(md_file, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        # Configure markdown extensions
        extensions = [
            'markdown.extensions.toc',
            'markdown.extensions.tables',
            'markdown.extensions.fenced_code',
            'markdown.extensions.codehilite'
        ]
        
        # Convert to HTML
        md = markdown.Markdown(extensions=extensions)
        html_content = md.convert(md_content)
        
        # Create complete HTML document with styling
        full_html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BIT1213 Operating System Final Assignment Report</title>
    <style>
        body {{
            font-family: 'Times New Roman', serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }}
        h1 {{
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }}
        h2 {{
            color: #34495e;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 5px;
            margin-top: 30px;
        }}
        h3 {{
            color: #2c3e50;
            margin-top: 25px;
        }}
        table {{
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        th {{
            background-color: #f2f2f2;
            font-weight: bold;
        }}
        code {{
            background-color: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }}
        pre {{
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
        }}
        blockquote {{
            border-left: 4px solid #3498db;
            margin: 0;
            padding-left: 20px;
            font-style: italic;
        }}
        .page-break {{
            page-break-before: always;
        }}
        @media print {{
            body {{
                margin: 0;
                padding: 15mm;
            }}
            h1, h2 {{
                page-break-after: avoid;
            }}
        }}
    </style>
</head>
<body>
{html_content}
</body>
</html>
"""
        
        # Write HTML file
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(full_html)
        
        print(f"✅ HTML created: {html_file}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating HTML: {e}")
        return False

def convert_html_to_pdf(html_file, pdf_file):
    """Convert HTML to PDF using pdfkit."""
    try:
        # PDF options
        options = {
            'page-size': 'A4',
            'margin-top': '0.75in',
            'margin-right': '0.75in',
            'margin-bottom': '0.75in',
            'margin-left': '0.75in',
            'encoding': "UTF-8",
            'no-outline': None,
            'enable-local-file-access': None
        }
        
        print(f"🔄 Converting {html_file} to {pdf_file}...")
        pdfkit.from_file(html_file, pdf_file, options=options)
        print(f"✅ PDF created: {pdf_file}")
        return True
        
    except Exception as e:
        print(f"❌ PDF conversion failed: {e}")
        print("Note: This might require wkhtmltopdf to be installed.")
        print("Download from: https://wkhtmltopdf.org/downloads.html")
        return False

def create_simple_text_report(md_file, txt_file):
    """Create a simple text version as backup."""
    try:
        with open(md_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Simple markdown to text conversion
        # Remove markdown formatting
        import re
        
        # Remove code blocks
        content = re.sub(r'```[\s\S]*?```', '[CODE BLOCK]', content)
        # Remove inline code
        content = re.sub(r'`([^`]+)`', r'\1', content)
        # Remove bold/italic
        content = re.sub(r'\*\*([^*]+)\*\*', r'\1', content)
        content = re.sub(r'\*([^*]+)\*', r'\1', content)
        # Remove headers markdown
        content = re.sub(r'^#+\s*', '', content, flags=re.MULTILINE)
        # Remove links
        content = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', content)
        
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write("BIT1213/BCC1213 OPERATING SYSTEM FINAL ASSIGNMENT REPORT\n")
            f.write("=" * 60 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(content)
        
        print(f"✅ Text backup created: {txt_file}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating text backup: {e}")
        return False

def main():
    """Main conversion function."""
    input_file = "BIT1213_Final_Assignment_Report.md"
    html_output = "BIT1213_Final_Assignment_Report.html"
    pdf_output = "BIT1213_Final_Assignment_Report.pdf"
    txt_output = "BIT1213_Final_Assignment_Report.txt"
    
    print("📄 Python-based Assignment Report Converter")
    print("=" * 50)
    
    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"❌ Input file not found: {input_file}")
        return False
    
    success_count = 0
    
    # Convert to HTML
    if create_html_from_markdown(input_file, html_output):
        success_count += 1
        
        # Try to convert HTML to PDF
        if convert_html_to_pdf(html_output, pdf_output):
            success_count += 1
    
    # Create text backup
    if create_simple_text_report(input_file, txt_output):
        success_count += 1
    
    # Summary
    print("\n📋 Conversion Summary:")
    print("=" * 30)
    print(f"HTML version: {'✅ Success' if os.path.exists(html_output) else '❌ Failed'}")
    print(f"PDF version: {'✅ Success' if os.path.exists(pdf_output) else '❌ Failed'}")
    print(f"Text backup: {'✅ Success' if os.path.exists(txt_output) else '❌ Failed'}")
    
    if os.path.exists(pdf_output):
        size_mb = os.path.getsize(pdf_output) / (1024 * 1024)
        print(f"\n🎉 PDF ready for submission: {pdf_output}")
        print(f"📊 File size: {size_mb:.2f} MB")
    elif os.path.exists(html_output):
        print(f"\n📄 HTML version available: {html_output}")
        print("💡 You can print this HTML file to PDF using your browser")
    
    return success_count > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
