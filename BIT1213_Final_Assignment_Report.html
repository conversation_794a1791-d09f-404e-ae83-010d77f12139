<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BIT1213/BCC1213 Operating System Final Assignment Report</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #24292f;
            background-color: #ffffff;
            max-width: 1012px;
            margin: 0 auto;
            padding: 45px;
        }
        
        h1, h2, h3, h4, h5, h6 {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
        }
        
        h1 {
            font-size: 2em;
            border-bottom: 1px solid #d0d7de;
            padding-bottom: 0.3em;
        }
        
        h2 {
            font-size: 1.5em;
            border-bottom: 1px solid #d0d7de;
            padding-bottom: 0.3em;
        }
        
        h3 {
            font-size: 1.25em;
        }
        
        p {
            margin-top: 0;
            margin-bottom: 16px;
        }
        
        ul, ol {
            margin-top: 0;
            margin-bottom: 16px;
            padding-left: 2em;
        }
        
        li {
            margin-bottom: 0.25em;
        }
        
        table {
            border-spacing: 0;
            border-collapse: collapse;
            margin-top: 0;
            margin-bottom: 16px;
            width: 100%;
        }
        
        table th, table td {
            padding: 6px 13px;
            border: 1px solid #d0d7de;
        }
        
        table th {
            font-weight: 600;
            background-color: #f6f8fa;
        }
        
        table tr:nth-child(2n) {
            background-color: #f6f8fa;
        }
        
        code {
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            background-color: rgba(175,184,193,0.2);
            border-radius: 6px;
            font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
        }
        
        pre {
            padding: 16px;
            overflow: auto;
            font-size: 85%;
            line-height: 1.45;
            background-color: #f6f8fa;
            border-radius: 6px;
            margin-bottom: 16px;
        }
        
        pre code {
            background-color: transparent;
            border: 0;
            padding: 0;
            margin: 0;
            font-size: 100%;
        }
        
        strong {
            font-weight: 600;
        }
        
        hr {
            height: 0.25em;
            padding: 0;
            margin: 24px 0;
            background-color: #d0d7de;
            border: 0;
        }
        
        blockquote {
            padding: 0 1em;
            color: #656d76;
            border-left: 0.25em solid #d0d7de;
            margin: 0 0 16px 0;
        }
    </style>
</head>
<body>
<h1>BIT1213/BCC1213 Operating System Final Assignment Report</h1>
<h2>Introduction</h2>
<h3>Project Objective</h3>
<p>This project demonstrates core operating system concepts through the development and performance analysis of a multi-threaded mathematical calculator. The primary objective is to showcase practical understanding of <strong>threading and concurrency</strong> - a fundamental OS concept that enables parallel execution of tasks to improve computational efficiency.</p>
<h3>Selected OS Concept: Multi-Threading</h3>
<p>We chose to implement <strong>multi-threading</strong> because it directly demonstrates several critical operating system principles:
- <strong>Thread Management</strong>: Creation, scheduling, and synchronization of multiple execution threads
- <strong>CPU Scheduling</strong>: How the operating system distributes computational tasks across available CPU cores
- <strong>Memory Management</strong>: Memory allocation patterns and resource sharing in concurrent environments
- <strong>System Resource Utilization</strong>: Monitoring and optimizing the use of system resources</p>
<h3>Project Significance</h3>
<p>Multi-threading is essential in modern computing for maximizing hardware utilization and improving application responsiveness. This project provides hands-on experience with threading concepts while demonstrating measurable performance differences between single-threaded and multi-threaded execution across different operating systems.</p>
<h3>Scope and Approach</h3>
<p>The project implements three distinct computational benchmarks:
1. <strong>Prime Number Search</strong> - CPU-intensive task with good parallelization potential
2. <strong>Mathematical Computations</strong> - Trigonometric calculations testing computational parallelization<br />
3. <strong>Memory Operations</strong> - Large data structure manipulation analyzing memory allocation patterns</p>
<hr />
<h2>Program Explanation</h2>
<h3>System Architecture</h3>
<p>The multi-threaded calculator follows a modular architecture designed for cross-platform compatibility and comprehensive performance monitoring:</p>
<pre><code>multithreaded_calculator/
├── main.py                    # Application entry point
├── calculator/
│   ├── operations.py          # Mathematical operations
│   ├── threading_manager.py   # Thread management and coordination
│   └── performance_monitor.py # Real-time performance tracking
├── utils/                     # Utility functions and helpers
├── tests/                     # Comprehensive unit tests
└── results/                   # Performance data and reports
    ├── windows_results/
    └── linux_results/
</code></pre>
<h3>Core Components</h3>
<h4>1. Mathematical Operations Module (<code>operations.py</code>)</h4>
<p>Implements three categories of computational tasks:
- <strong>Prime Search Algorithm</strong>: Efficiently finds prime numbers in a given range using optimized trial division
- <strong>CPU Intensive Operations</strong>: Performs trigonometric calculations with floating-point arithmetic
- <strong>Memory Intensive Operations</strong>: Creates and manipulates large data structures (30MB+)</p>
<h4>2. Threading Manager (<code>threading_manager.py</code>)</h4>
<p>Manages thread execution using Python's <code>concurrent.futures.ThreadPoolExecutor</code>:
- <strong>Thread Pool Management</strong>: Configurable worker thread pools (1, 2, 4, 8 threads)
- <strong>Task Distribution</strong>: Intelligent work partitioning across available threads
- <strong>Result Aggregation</strong>: Thread-safe collection and combination of results
- <strong>Performance Benchmarking</strong>: Automated comparison of single vs multi-threaded execution</p>
<h4>3. Performance Monitor (<code>performance_monitor.py</code>)</h4>
<p>Provides comprehensive system monitoring capabilities:
- <strong>Real-time Metrics</strong>: CPU usage, memory consumption, thread count tracking
- <strong>Data Collection</strong>: Configurable sampling intervals for accurate measurements
- <strong>Export Functionality</strong>: JSON and CSV output for analysis
- <strong>System Information</strong>: Hardware and OS configuration details</p>
<h3>Technical Implementation</h3>
<h4>Threading Strategy</h4>
<p>The application uses Python's built-in <code>ThreadPoolExecutor</code> for robust thread management:</p>
<pre><code class="language-python">with ThreadPoolExecutor(max_workers=thread_count) as executor:
    futures = [executor.submit(task_function, *args) for _ in range(thread_count)]
    results = [future.result() for future in as_completed(futures)]
</code></pre>
<p>This approach provides:
- <strong>Automatic Thread Lifecycle</strong>: Creation, scheduling, and cleanup handled by the OS
- <strong>Exception Handling</strong>: Proper error propagation from worker threads
- <strong>Resource Control</strong>: Configurable maximum worker threads to prevent resource exhaustion
- <strong>Cross-Platform Consistency</strong>: Uniform behavior across Windows and Linux</p>
<h4>Performance Monitoring Implementation</h4>
<p>A dedicated monitoring thread runs concurrently to collect performance metrics:</p>
<pre><code class="language-python">def _monitor_loop(self):
    while self.monitoring:
        data_point = {
            'timestamp': time.time(),
            'cpu_percent_total': psutil.cpu_percent(),
            'memory_rss_mb': self.process.memory_info().rss / (1024 * 1024),
            'thread_count': threading.active_count()
        }
        self.performance_data.append(data_point)
        time.sleep(self.sampling_interval)
</code></pre>
<hr />
<h2>OS Environment and Setup</h2>
<h3>Development Environment</h3>
<h4>Primary Testing Platform (Windows)</h4>
<ul>
<li><strong>Operating System</strong>: Windows 11 Professional (64-bit)</li>
<li><strong>Hardware Configuration</strong>:</li>
<li>CPU: Intel processor with 10 physical cores, 12 logical cores</li>
<li>Memory: 15.69 GB total RAM</li>
<li>Storage: SSD for optimal I/O performance</li>
<li><strong>Python Environment</strong>: Python 3.12.2 with virtual environment isolation</li>
</ul>
<h4>Secondary Testing Platform (Linux)</h4>
<ul>
<li><strong>Target Systems</strong>: Ubuntu 20.04+ LTS, CentOS 8+</li>
<li><strong>Compatibility</strong>: Designed for broad Linux distribution support</li>
<li><strong>Testing Scripts</strong>: Dedicated Linux benchmark scripts prepared for validation</li>
</ul>
<h3>Software Dependencies</h3>
<p>The project uses carefully selected, stable dependencies:</p>
<p><strong>Core Dependencies:</strong>
- <code>psutil&gt;=5.9.0</code> - Cross-platform system and process monitoring
- <code>numpy&gt;=1.21.0</code> - Efficient numerical computations
- <code>matplotlib&gt;=3.5.0</code> - Performance visualization and charting
- <code>pandas&gt;=1.3.0</code> - Data analysis and export capabilities</p>
<p><strong>Development Tools:</strong>
- <code>pytest&gt;=7.0.0</code> - Comprehensive unit testing framework
- <code>concurrent.futures</code> - Built-in Python threading support</p>
<h3>Environment Setup Process</h3>
<h4>1. System Verification</h4>
<pre><code class="language-bash"># Verify Python version (3.8+ required)
python --version

# Check system resources
python setup_environment.py
</code></pre>
<h4>2. Dependency Installation</h4>
<pre><code class="language-bash"># Install all required packages
pip install -r requirements.txt

# Verify installation
python -c &quot;import psutil, numpy, matplotlib, pandas; print('All dependencies ready')&quot;
</code></pre>
<h4>3. Performance Monitoring Tools</h4>
<p><strong>Windows Tools:</strong>
- <strong>Task Manager</strong>: Real-time CPU and memory monitoring
- <strong>Performance Monitor (perfmon)</strong>: Detailed system metrics
- <strong>Resource Monitor</strong>: Thread and process analysis</p>
<p><strong>Linux Tools:</strong>
- <strong>top/htop</strong>: Process and CPU monitoring
- <strong>vmstat</strong>: Virtual memory statistics<br />
- <strong>iostat</strong>: I/O performance metrics
- <strong>time command</strong>: Execution time measurement</p>
<h3>Cross-Platform Compatibility Design</h3>
<p>The application was architected for seamless cross-platform operation:
- <strong>Pure Python Implementation</strong>: No platform-specific dependencies
- <strong>Consistent API Usage</strong>: Uniform threading model across operating systems
- <strong>Portable Monitoring</strong>: <code>psutil</code> provides consistent metrics on both platforms
- <strong>Adaptive File Paths</strong>: Dynamic path resolution for different file systems</p>
<hr />
<h2>Testing Results and Analysis</h2>
<h3>Part 1 - Program Development (Code Implementation)</h3>
<h4>Implementation Success Metrics</h4>
<p>✅ <strong>Complete Functionality</strong>: All core features implemented and tested<br />
✅ <strong>Threading Integration</strong>: Successful ThreadPoolExecutor implementation<br />
✅ <strong>Performance Monitoring</strong>: Real-time metrics collection operational<br />
✅ <strong>Cross-Platform Design</strong>: Code runs identically on Windows and Linux<br />
✅ <strong>Error Handling</strong>: Robust exception management and graceful degradation  </p>
<h4>Code Quality Indicators</h4>
<ul>
<li><strong>Modular Architecture</strong>: Clear separation of concerns across modules</li>
<li><strong>Comprehensive Testing</strong>: Unit tests covering all major components</li>
<li><strong>Documentation</strong>: Detailed inline documentation and usage guides</li>
<li><strong>Standards Compliance</strong>: Follows Python PEP 8 coding standards</li>
</ul>
<h3>Part 2 - Cross-Platform Testing</h3>
<h4>Windows Platform Testing Results</h4>
<p><strong>System Configuration:</strong>
- OS: Windows 11 Professional
- CPU: 10 physical cores, 12 logical cores<br />
- Memory: 15.69 GB
- Python: 3.12.2</p>
<p><strong>Performance Benchmark Results:</strong></p>
<table>
<thead>
<tr>
<th>Benchmark Type</th>
<th>Threads</th>
<th>Execution Time</th>
<th>Speedup</th>
<th>Efficiency</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Prime Search (1-50,000)</strong></td>
<td>1</td>
<td>0.0556s</td>
<td>1.00x</td>
<td>100.0%</td>
</tr>
<tr>
<td></td>
<td>2</td>
<td>0.0478s</td>
<td>1.16x</td>
<td>58.2%</td>
</tr>
<tr>
<td></td>
<td>4</td>
<td>0.0445s</td>
<td>1.25x</td>
<td>31.2%</td>
</tr>
<tr>
<td></td>
<td>8</td>
<td>0.0421s</td>
<td>1.32x</td>
<td>16.5%</td>
</tr>
<tr>
<td><strong>CPU Intensive (500k iterations)</strong></td>
<td>1</td>
<td>0.1429s</td>
<td>1.00x</td>
<td>100.0%</td>
</tr>
<tr>
<td></td>
<td>2</td>
<td>0.1286s</td>
<td>1.11x</td>
<td>55.6%</td>
</tr>
<tr>
<td></td>
<td>4</td>
<td>0.1376s</td>
<td>1.04x</td>
<td>26.0%</td>
</tr>
<tr>
<td></td>
<td>8</td>
<td>0.1257s</td>
<td>1.14x</td>
<td>14.2%</td>
</tr>
<tr>
<td><strong>Memory Intensive (30MB)</strong></td>
<td>1</td>
<td>0.3733s</td>
<td>1.00x</td>
<td>100.0%</td>
</tr>
<tr>
<td></td>
<td>2</td>
<td>0.4051s</td>
<td>0.92x</td>
<td>46.1%</td>
</tr>
<tr>
<td></td>
<td>4</td>
<td>0.4163s</td>
<td>0.90x</td>
<td>22.4%</td>
</tr>
</tbody>
</table>
<h4>Linux Platform Preparation</h4>
<ul>
<li><strong>Testing Scripts</strong>: Comprehensive Linux benchmark scripts developed</li>
<li><strong>Tool Integration</strong>: Prepared integration with Linux performance monitoring tools</li>
<li><strong>Expected Differences</strong>: Anticipated variations in thread scheduling and memory management</li>
</ul>
<h3>Part 3 - Analysis &amp; Report</h3>
<h4>Performance Analysis Insights</h4>
<p><strong>Threading Effectiveness:</strong>
- <strong>Prime Search</strong>: Best performance with 1.32x speedup using 8 threads
- <strong>CPU Intensive</strong>: Limited improvement (1.14x) due to Python GIL constraints
- <strong>Memory Intensive</strong>: Performance degradation due to memory contention</p>
<p><strong>Efficiency Trends:</strong>
- <strong>Diminishing Returns</strong>: Efficiency decreases significantly with higher thread counts
- <strong>Optimal Configuration</strong>: 2-4 threads provide best efficiency for most tasks
- <strong>Overhead Impact</strong>: Thread management overhead becomes significant beyond optimal count</p>
<p><strong>System Resource Utilization:</strong>
- <strong>CPU Usage</strong>: Effective utilization of multiple cores during peak execution
- <strong>Memory Patterns</strong>: Stable consumption with predictable thread overhead
- <strong>Thread Management</strong>: Successful creation, scheduling, and cleanup observed</p>
<h4>Operating System Behavior Observations</h4>
<p><strong>Windows Thread Scheduling:</strong>
- Effective load balancing across logical CPU cores
- Consistent thread creation and destruction patterns
- Measurable context switching overhead in performance metrics</p>
<p><strong>Resource Management:</strong>
- Virtual memory management handled efficiently by Windows
- Garbage collection coordination across threads
- Resource contention resolution mechanisms observed</p>
<h3>Part 4 - Reflection and Documentation</h3>
<h4>Learning Outcomes</h4>
<p><strong>Technical Understanding:</strong>
- <strong>Thread Lifecycle Management</strong>: Gained deep understanding of thread creation, execution, and termination
- <strong>Synchronization Mechanisms</strong>: Learned about thread coordination and result aggregation
- <strong>Performance Optimization</strong>: Understood the balance between parallelization benefits and overhead costs</p>
<p><strong>Operating System Insights:</strong>
- <strong>CPU Scheduling</strong>: Observed how Windows distributes threads across available cores
- <strong>Memory Management</strong>: Analyzed allocation patterns in multi-threaded environments
- <strong>Resource Contention</strong>: Identified bottlenecks and optimization opportunities</p>
<h4>Challenges Encountered</h4>
<p><strong>Python GIL Limitations:</strong>
- <strong>Challenge</strong>: Global Interpreter Lock limits true CPU parallelism
- <strong>Solution</strong>: Focused on demonstrating threading concepts and coordination mechanisms
- <strong>Learning</strong>: Understood the importance of language choice for parallel computing</p>
<p><strong>Performance Measurement Accuracy:</strong>
- <strong>Challenge</strong>: Obtaining consistent, accurate measurements in concurrent environment
- <strong>Solution</strong>: Implemented separate monitoring thread with high-resolution timing
- <strong>Learning</strong>: Importance of proper measurement methodology in performance analysis</p>
<p><strong>Cross-Platform Considerations:</strong>
- <strong>Challenge</strong>: Ensuring consistent behavior across different operating systems
- <strong>Solution</strong>: Used platform-agnostic libraries and extensive testing
- <strong>Learning</strong>: Value of portable design and comprehensive testing strategies</p>
<h4>OS Concepts Successfully Demonstrated</h4>
<ol>
<li><strong>Threading and Concurrency</strong>: ThreadPoolExecutor implementation showcasing thread management</li>
<li><strong>CPU Scheduling</strong>: Observable thread distribution across multiple CPU cores</li>
<li><strong>Memory Management</strong>: Analysis of allocation patterns and garbage collection impact</li>
<li><strong>System Resource Management</strong>: Monitoring and optimization of resource utilization</li>
</ol>
<h4>Project Impact and Future Applications</h4>
<p>This project provides a solid foundation for understanding parallel computing concepts and their practical implementation. The skills and insights gained are directly applicable to:
- High-performance computing applications
- Concurrent system design
- Performance optimization strategies
- Cross-platform software development</p>
<hr />
<p><strong>Conclusion</strong></p>
<p>The multi-threaded calculator project successfully demonstrates core operating system concepts through practical implementation and comprehensive analysis. The measurable performance improvements, detailed monitoring capabilities, and cross-platform design showcase a thorough understanding of threading principles and their real-world applications in modern computing environments.</p>
</body>
</html>