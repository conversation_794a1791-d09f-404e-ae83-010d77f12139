
=== 第 1 页 ===
 
BIT1213/BCC1213 -OPERATING SYSTEM  
Final Assessment (40%)  
 
Instruction:  
1. This assignment is group work and contributes 40% of the overall assessment.  
2. Mode: Group of 2-3 students   
3. Submission in the form of written report (Hard Copy & Soft Copy)  
a. One printed copy (by group)  
b<PERSON> <PERSON> (Individual)  
4. Date of Submission: 4 August  202 5 (MONDAY ) 
Objective of the Assignment  
This practical assignment is designed to assess your understanding and application of core 
operating system (OS) concepts, including memory management, process/thread handling, 
and file system operations. You will develop a small -scale software solution th at 
demonstrates these OS -level functionalities and test its performance on two different 
operating systems (e.g., Windows and Linux).  
CLO3: Demonstrate a simple software solution and test its performance across various 
operating systems (OS) (P5, PLO3)  
Assignment Overview  
You are required to complete the following:  
1. Design and implement a simple software system that demonstrates at least one OS -level 
functionality (e.g., memory usage, multithreading, file I/O, or system commands).  
2. Execute the program on at least two different operating systems (e.g., Windows and Linux).  
3. Record performance metrics such as execution time, memory usage, and CPU utilization 
using OS tools (e.g., time, top, Task Manager).  
4. Analyze and compare the performance data between the operating systems.  
5. Submit a report that includes system explanation, testing outcomes, analysis, and a 
personal reflection on the OS concepts demonstrated.  
6. Present your solution and findings in a short presentation.  


=== 第 2 页 ===
Suggested Project Titles / Software Ideas  
Choose ONE of the following software ideas, or propose your own (subject to instructor 
approval):  
 
1. Simple File Organizer  
– Organize files by type, date, or size and compare I/O efficiency.  
 
2. Multi -Threaded Calculator  
– Perform arithmetic using multiple threads; test CPU usage and thread behavior.  
 
3. Memory Benchmark Tool  
– Allocate and manipulate data structures; measure memory usage.  
 
4. Custom Shell  
 – Accept and execute basic commands (e.g., ls, mkdir); explore system call execution.  
 
5. Simple Process Monitor – Display current processes, CPU usage, and PID across OSes.  
 
Assignment Structure  
Your report must contain the following components:  
Part 1 – Program Development (Code) : 
• Implement a working system -level software program.  
• Include at least one OS concept (e.g., threading, memory tracking, file handling).  
• Ensure the program runs correctly and provides output.  
 
Part 2 – Cross -Platform Testing : 
• Test the program on Windows and Linux.  
• Use system tools (e.g., top, time, Task Manager) to record execution time, memory, and 
CPU.  
 
Part 3 – Analysis & Report : 
• Compare OS behaviors using the collected data.  
• Include screenshots, performance charts, tables, and interpretations.  
 
Part 4 – Reflection and Documentation : 
• Reflect on what you learned.  
• Explain OS concept applied, challenges encountered, and how the program demonstrates 
OS principles.  
 

=== 第 3 页 ===
Deliverables and Submission Format  
Each student/group must submit:  
• Source code (.java, .py, or .cpp)  
• Report (PDF, 5 –10 pages) containing:  
   - Introduction  
   - Program explanation  
   - OS environments and setup  
   - Test results and analysis (Part 1 to Part 4)  
 
Code Requirements  
Your software must:  
• Be developed using Java, Python, or C++.  
• Contain at least one operating system concept (threading, memory, file I/O, or system 
call).  
• Be executable on both Windows and Linux.  
• Produce meaningful, observable output.  
• Be your original work.  
 
 

=== 第 4 页 ===
Assessment Rubric (Total: 100 marks ) 
Criteria  Excellent (5)  Good (4)  Satisfactory 
(3) Fair (2)  Needs 
Improvement 
(0–1) Score 
(Max)  
1. Clarity of 
Objective 
(10%)  Clearly explains the 
project goal and 
demonstrates 
strong 
understanding of OS 
concept used.  Mostly clear 
explanation of 
the goal; good 
understanding of 
the OS concept.  General 
explanation 
with some 
basic 
understanding 
of OS concept.  Limited 
explanation or 
unclear 
objective; weak 
understanding.  Missing or 
incorrect 
explanation of 
project 
purpose and OS 
concept.   
2. Code 
Implementatio
n (25%)  Code is complete, 
well -structured, 
runs without error, 
and meets the 
intended function.  Code mostly 
works well; 
minor issues or 
incomplete 
elements.  Basic 
functionality is 
present; may 
lack polish or 
completeness.  Partially 
functioning 
code; several 
issues with 
execution.  Code does not 
run or is 
incomplete 
with major 
errors.   
3. OS Concept 
Application 
(20%) Effectively applies 
OS concept(s) such 
as threading, 
memory, or file 
handling with 
depth.  Applies at least 
one OS concept 
with general 
accuracy and 
purpose.  Applies OS 
concept but 
lacks clarity or 
depth in use.  Weak attempt 
to apply OS 
concept; not 
clearly 
demonstrated.  No clear 
application of 
OS concept or 
incorrect 
implementatio
n.  
4. Testing & 
Analysis ( 15%) Program tested on 2 
OS platforms; 
analysis includes 
performance 
metrics and clear 
comparison.  Tested on both 
platforms; 
general 
comparison 
provided.  Basic testing 
done; minimal 
comparison or 
missing key 
metrics.  Limited testing 
or unclear 
comparison 
between 
platforms.  No testing on 
different OS or 
missing 
comparison.   
5. Reflection & 
Discussion 
(20%)  Provides deep 
insights into 
learning experience, 
OS behavior  
observed, and 
critical reflection on 
challenges and 
outcomes.  Good reflection 
on learning and 
OS behavior; 
some critical 
points are 
addressed.  Basic 
reflection 
included with 
limited 
discussion of 
challenges or 
learning.  Minimal 
reflection; 
unclear what 
was learned or 
experienced.  No meaningful 
reflection; 
lacks 
connection to 
the work done.   
6. Report 
Presentation 
(10%) Report is well -
organized, 
professional, 
includes 
screenshots, 
outputs, and 
reflection.  Well -written 
with few 
formatting 
issues; most 
required 
elements are 
included.  Readable 
report with 
minor 
organization 
or content 
gaps.  Report is hard 
to follow; 
missing 
elements or 
poor 
formatting.  Poorly 
presented, 
unstructured, 
and lacks 
required 
documentation.   
 
Total Marks: 100 marks   
 
 
 

=== 第 5 页 ===
 
 
 
 
BACHELOR OF INFORMATION TECHNOLOGY  (HONS)  
 
 
MAY  2025 FINAL ASSESSMENT  
 
 
 
COURSE: OPERATING SYSTEM  
COURSE CODE: BIT1213/BCC1213  
TOPIC:   FINAL ASSESSMENT   
DUE DATE: 4 August  2025  
 
 
Student ID  Student Name  
  
  
  
 

